import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import redis from "@/lib/redis";

// 缓存键
const CACHE_KEY = "sitemap:tags:xml";
const CACHE_TTL = 3600; // 1小时

// 定义标签统计接口
interface TagStats {
  tag: string;
  count: number;
  lastUsed: Date;
}

/**
 * 生成标签页面的sitemap XML
 */
export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";

    // 尝试从缓存获取
    const cached = await redis?.get(CACHE_KEY);
    if (cached) {
      return new NextResponse(cached, {
        headers: {
          "Content-Type": "application/xml",
          "Cache-Control": "public, max-age=3600, s-maxage=3600",
        },
      });
    }

    // 获取所有工具的标签
    const tools = await prisma.tool.findMany({
      select: {
        tags: true,
        updatedAt: true,
      },
      where: {
        tags: {
          not: {
            isEmpty: true,
          },
        },
      },
    });

    // 统计标签使用频率
    const tagStats = new Map<string, { count: number; lastUsed: Date }>();

    tools.forEach((tool) => {
      tool.tags.forEach((tag) => {
        const existing = tagStats.get(tag);
        if (existing) {
          existing.count++;
          if (tool.updatedAt > existing.lastUsed) {
            existing.lastUsed = tool.updatedAt;
          }
        } else {
          tagStats.set(tag, {
            count: 1,
            lastUsed: tool.updatedAt,
          });
        }
      });
    });

    // 转换为数组并排序（只取前50个热门标签）
    const popularTags: TagStats[] = Array.from(tagStats.entries())
      .map(([tag, stats]) => ({
        tag,
        count: stats.count,
        lastUsed: stats.lastUsed,
      }))
      .filter((tagStat) => tagStat.count >= 2) // 至少被使用2次
      .sort((a, b) => b.count - a.count)
      .slice(0, 50);

    // 计算标签优先级
    function calculateTagPriority(count: number): string {
      let priority = 0.3;
      if (count > 20) {
        priority = 0.6;
      } else if (count > 10) {
        priority = 0.5;
      } else if (count > 5) {
        priority = 0.4;
      }
      return priority.toFixed(1);
    }

    // 生成XML
    const urls = popularTags
      .map((tagStat) => {
        const url = `${baseUrl}/search?q=${encodeURIComponent(tagStat.tag)}`;
        const lastmod = tagStat.lastUsed.toISOString();
        const priority = calculateTagPriority(tagStat.count);

        return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
      })
      .join("\n");

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;

    // 缓存结果
    if (redis) {
      await redis.setex(CACHE_KEY, CACHE_TTL, sitemap);
    }

    return new NextResponse(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600",
      },
    });
  } catch (error) {
    console.error("Error generating tags sitemap:", error);

    // 返回空的sitemap
    const emptySitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new NextResponse(emptySitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=300, s-maxage=300",
      },
    });
  }
}
