import { MetadataRoute } from "next";
import prisma from "@/lib/prisma";
import redis from "@/lib/redis";

// 定义站点地图条目的接口
interface SitemapEntry {
  url: string;
  lastModified?: string | Date;
  changeFrequency?:
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never";
  priority?: number;
}

// 定义标签统计接口
interface TagStats {
  tag: string;
  count: number;
  lastUsed: Date;
}

// 缓存键常量
const CACHE_KEYS = {
  SITEMAP: "sitemap:main",
  CATEGORIES: "sitemap:categories",
  TOOLS: "sitemap:tools",
  TAGS: "sitemap:tags",
  LAST_MODIFIED: "sitemap:last_modified",
} as const;

// 缓存过期时间（秒）
const CACHE_TTL = {
  SITEMAP: 3600, // 1小时
  DATA: 1800, // 30分钟
} as const;

// 获取分类数据（带缓存）
async function getCategories() {
  try {
    // 尝试从缓存获取
    const cached = await redis?.get(CACHE_KEYS.CATEGORIES);
    if (cached) {
      return JSON.parse(cached);
    }

    const categories = await prisma.category.findMany({
      select: {
        id: true,
        slug: true,
        updatedAt: true,
        _count: {
          select: {
            tools: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    // 缓存结果
    if (redis) {
      await redis.setex(
        CACHE_KEYS.CATEGORIES,
        CACHE_TTL.DATA,
        JSON.stringify(categories)
      );
    }

    return categories;
  } catch (error) {
    console.error("Error fetching categories for sitemap:", error);
    return [];
  }
}

// 获取工具数据（带缓存）
async function getTools() {
  try {
    // 尝试从缓存获取
    const cached = await redis?.get(CACHE_KEYS.TOOLS);
    if (cached) {
      return JSON.parse(cached);
    }

    const tools = await prisma.tool.findMany({
      select: {
        id: true,
        updatedAt: true,
        views: true,
        isFeatured: true,
        lastViewedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    // 缓存结果
    if (redis) {
      await redis.setex(
        CACHE_KEYS.TOOLS,
        CACHE_TTL.DATA,
        JSON.stringify(tools)
      );
    }

    return tools;
  } catch (error) {
    console.error("Error fetching tools for sitemap:", error);
    return [];
  }
}

// 获取热门标签数据（带缓存）
async function getPopularTags(): Promise<TagStats[]> {
  try {
    // 尝试从缓存获取
    const cached = await redis?.get(CACHE_KEYS.TAGS);
    if (cached) {
      return JSON.parse(cached);
    }

    // 获取所有工具的标签
    const tools = await prisma.tool.findMany({
      select: {
        tags: true,
        updatedAt: true,
      },
      where: {
        tags: {
          not: {
            isEmpty: true,
          },
        },
      },
    });

    // 统计标签使用频率
    const tagStats = new Map<string, { count: number; lastUsed: Date }>();

    tools.forEach((tool) => {
      tool.tags.forEach((tag) => {
        const existing = tagStats.get(tag);
        if (existing) {
          existing.count++;
          if (tool.updatedAt > existing.lastUsed) {
            existing.lastUsed = tool.updatedAt;
          }
        } else {
          tagStats.set(tag, {
            count: 1,
            lastUsed: tool.updatedAt,
          });
        }
      });
    });

    // 转换为数组并排序（只取前20个热门标签）
    const popularTags: TagStats[] = Array.from(tagStats.entries())
      .map(([tag, stats]) => ({
        tag,
        count: stats.count,
        lastUsed: stats.lastUsed,
      }))
      .filter((tagStat) => tagStat.count >= 2) // 至少被使用2次
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);

    // 缓存结果
    if (redis) {
      await redis.setex(
        CACHE_KEYS.TAGS,
        CACHE_TTL.DATA,
        JSON.stringify(popularTags)
      );
    }

    return popularTags;
  } catch (error) {
    console.error("Error fetching popular tags for sitemap:", error);
    return [];
  }
}

// 计算工具优先级（基于浏览量、是否精选等）
function calculateToolPriority(tool: {
  views: number;
  isFeatured: boolean;
  lastViewedAt: Date | null;
}): number {
  let priority = 0.6; // 基础优先级

  // 精选工具优先级更高
  if (tool.isFeatured) {
    priority += 0.2;
  }

  // 根据浏览量调整优先级
  if (tool.views > 1000) {
    priority += 0.1;
  } else if (tool.views > 100) {
    priority += 0.05;
  }

  // 最近浏览过的工具优先级更高
  if (tool.lastViewedAt) {
    const daysSinceLastView =
      (Date.now() - tool.lastViewedAt.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceLastView < 7) {
      priority += 0.1;
    } else if (daysSinceLastView < 30) {
      priority += 0.05;
    }
  }

  return Math.min(priority, 1.0); // 确保不超过1.0
}

// 计算分类优先级（基于工具数量）
function calculateCategoryPriority(toolCount: number): number {
  let priority = 0.5; // 基础优先级

  if (toolCount > 50) {
    priority = 0.8;
  } else if (toolCount > 20) {
    priority = 0.7;
  } else if (toolCount > 5) {
    priority = 0.6;
  }

  return priority;
}

// 计算标签优先级（基于使用频率）
function calculateTagPriority(count: number): number {
  let priority = 0.3; // 基础优先级

  if (count > 20) {
    priority = 0.6;
  } else if (count > 10) {
    priority = 0.5;
  } else if (count > 5) {
    priority = 0.4;
  }

  return priority;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";
  const now = new Date();

  try {
    // 尝试从缓存获取完整的sitemap
    const cachedSitemap = await redis?.get(CACHE_KEYS.SITEMAP);
    if (cachedSitemap) {
      console.log("Returning cached sitemap");
      return JSON.parse(cachedSitemap);
    }

    console.log("Generating fresh sitemap");

    // 静态路由
    const staticRoutes: SitemapEntry[] = [
      {
        url: baseUrl,
        lastModified: now,
        changeFrequency: "daily",
        priority: 1.0,
      },
      {
        url: `${baseUrl}/search`,
        lastModified: now,
        changeFrequency: "weekly",
        priority: 0.8,
      },
    ];

    // 并行获取所有数据
    const [categories, tools, popularTags] = await Promise.all([
      getCategories(),
      getTools(),
      getPopularTags(),
    ]);

    console.log(
      `Sitemap data: ${categories.length} categories, ${tools.length} tools, ${popularTags.length} tags`
    );

    // 分类页面
    const categoryRoutes: SitemapEntry[] = categories.map((category) => ({
      url: `${baseUrl}/category/${category.slug || category.id}`,
      lastModified: category.updatedAt || now,
      changeFrequency: "weekly" as const,
      priority: calculateCategoryPriority(category._count?.tools || 0),
    }));

    // 工具详情页
    const toolRoutes: SitemapEntry[] = tools.map((tool) => ({
      url: `${baseUrl}/tool/${tool.id}`,
      lastModified: tool.updatedAt || now,
      changeFrequency: "daily" as const,
      priority: calculateToolPriority(tool),
    }));

    // 标签页面（为热门标签创建搜索页面链接）
    const tagRoutes: SitemapEntry[] = popularTags.map((tagStat) => ({
      url: `${baseUrl}/search?q=${encodeURIComponent(tagStat.tag)}`,
      lastModified: tagStat.lastUsed,
      changeFrequency: "weekly" as const,
      priority: calculateTagPriority(tagStat.count),
    }));

    const allRoutes = [
      ...staticRoutes,
      ...categoryRoutes,
      ...toolRoutes,
      ...tagRoutes,
    ];

    console.log(`Generated sitemap with ${allRoutes.length} total entries`);

    // 缓存生成的sitemap
    if (redis) {
      await redis.setex(
        CACHE_KEYS.SITEMAP,
        CACHE_TTL.SITEMAP,
        JSON.stringify(allRoutes)
      );
      await redis.set(CACHE_KEYS.LAST_MODIFIED, now.toISOString());
    }

    return allRoutes as MetadataRoute.Sitemap;
  } catch (error) {
    console.error("生成站点地图时出错:", error);

    // 降级策略：返回基本的静态路由
    const fallbackRoutes: SitemapEntry[] = [
      {
        url: baseUrl,
        lastModified: now,
        changeFrequency: "daily",
        priority: 1.0,
      },
      {
        url: `${baseUrl}/search`,
        lastModified: now,
        changeFrequency: "weekly",
        priority: 0.8,
      },
    ];

    return fallbackRoutes as MetadataRoute.Sitemap;
  }
}
